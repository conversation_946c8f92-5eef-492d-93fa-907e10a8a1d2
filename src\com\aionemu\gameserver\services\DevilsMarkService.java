package com.aionemu.gameserver.services;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ScheduledFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.aionemu.commons.utils.Rnd;
import com.aionemu.gameserver.configs.main.CustomConfig;
import com.aionemu.gameserver.model.ChatType;
import com.aionemu.gameserver.model.gameobjects.player.CustomPlayerState;
import com.aionemu.gameserver.model.gameobjects.player.Player;
import com.aionemu.gameserver.services.abyss.AbyssPointsService;
import com.aionemu.gameserver.services.abyss.GloryPointsService;
import com.aionemu.gameserver.skillengine.SkillEngine;
import com.aionemu.gameserver.skillengine.model.Effect;
import com.aionemu.gameserver.utils.PacketSendUtility;
import com.aionemu.gameserver.utils.ThreadPoolManager;
import com.aionemu.gameserver.world.World;
import com.aionemu.gameserver.controllers.observer.ActionObserver;
import com.aionemu.gameserver.controllers.observer.ObserverType;

/**
 * The Devil's Mark - A PvP bounty hunting system
 * Every 3 hours, one random player gets marked by the Devil of Aion.
 * Other players must hunt and kill the marked player to receive rewards.
 *
 * <AUTHOR> Implementation
 */
public class DevilsMarkService {

    private static final Logger log = LoggerFactory.getLogger(DevilsMarkService.class);

    private Player currentMarkedPlayer;
    private ScheduledFuture<?> markTask;
    private ScheduledFuture<?> unmarkTask;
    private ScheduledFuture<?> healingTask;
    private long markStartTime;
    private ActionObserver reviveObserver;

    private static class SingletonHolder {
        protected static final DevilsMarkService instance = new DevilsMarkService();
    }

    public static DevilsMarkService getInstance() {
        return SingletonHolder.instance;
    }

    private DevilsMarkService() {
        // Private constructor for singleton
    }

    /**
     * Initialize the Devil's Mark service
     */
    public void init() {
        if (!CustomConfig.DEVILS_MARK_ENABLED) {
            log.info("Devil's Mark system is disabled.");
            return;
        }

        log.info("Initializing Devil's Mark service...");
        scheduleNextMarking();
        log.info("Devil's Mark service initialized successfully!");
    }

    /**
     * Schedule the next marking event
     */
    private void scheduleNextMarking() {
        if (markTask != null) {
            markTask.cancel(false);
        }

        // Schedule next marking in configured hours
        int durationMs = CustomConfig.DEVILS_MARK_DURATION_HOURS * 60 * 60 * 1000;
        markTask = ThreadPoolManager.getInstance().schedule(() -> {
            selectAndMarkRandomPlayer();
        }, durationMs);

        log.info("Next Devil's Mark event scheduled in {} hours", CustomConfig.DEVILS_MARK_DURATION_HOURS);
    }

    /**
     * Select a random eligible player and mark them
     */
    private void selectAndMarkRandomPlayer() {
        List<Player> eligiblePlayers = getEligiblePlayers();

        log.info("DEBUG: selectAndMarkRandomPlayer - found {} eligible players", eligiblePlayers.size());

        if (eligiblePlayers.isEmpty()) {
            log.warn("No eligible players found for Devil's Mark. Rescheduling...");
            scheduleNextMarking();
            return;
        }

        Player selectedPlayer = Rnd.get(eligiblePlayers);
        log.info("DEBUG: Selected new marked player: {}", selectedPlayer.getName());
        markPlayer(selectedPlayer);
    }

    /**
     * Get list of players eligible to be marked
     */
    private List<Player> getEligiblePlayers() {
        List<Player> eligible = new ArrayList<>();

        World.getInstance().forEachPlayer(player -> {
            if (isEligibleForMarking(player)) {
                eligible.add(player);
            }
        });

        return eligible;
    }

    /**
     * Check if a player is eligible to be marked
     */
    private boolean isEligibleForMarking(Player player) {
        return player.isOnline()
            && player.getLevel() >= CustomConfig.DEVILS_MARK_MIN_LEVEL
            && !player.isInCustomState(CustomPlayerState.EVENT_MODE)
            && !player.isInCustomState(CustomPlayerState.INVULNERABLE)
            && !player.isInInstance();
    }

    /**
     * Mark a player with the Devil's Mark
     */
    private void markPlayer(Player player) {
        if (currentMarkedPlayer != null) {
            unmarkCurrentPlayer(false);
        }

        currentMarkedPlayer = player;
        markStartTime = System.currentTimeMillis();

        // Apply the devil's mark state
        player.setCustomState(CustomPlayerState.DEVILS_MARK);

        // Also set as enemy of all players to force client-side attackability
        player.setCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS);

        // Update player attributes to inform other players about the state change
        player.getController().onChangedPlayerAttributes();

        // Update protection status and broadcast to nearby players
        updateProtectionStatus(player);

        // Apply visual effects (multiple effects supported)
        applyVisualEffects(player);

        // Send announcement to all players
        announceMarkedPlayer(player);

        // Send message to marked player
        PacketSendUtility.sendMessage(player,
            "You have been marked by the Devil of Aion! Other players will hunt you for the next " +
            CustomConfig.DEVILS_MARK_DURATION_HOURS + " hours. Survive to earn rewards!",
            ChatType.BRIGHT_YELLOW_CENTER);

        // Schedule unmarking
        int durationMs = CustomConfig.DEVILS_MARK_DURATION_HOURS * 60 * 60 * 1000;
        unmarkTask = ThreadPoolManager.getInstance().schedule(() -> {
            // Player survived the full duration - give rewards and immediately select next player
            String survivorName = currentMarkedPlayer != null ? currentMarkedPlayer.getName() : "unknown";
            unmarkCurrentPlayerWithoutScheduling(true);
            log.info("DEBUG: Marked player {} survived the duration, immediately selecting new marked player", survivorName);
            selectAndMarkRandomPlayer();
        }, durationMs);

        // Add observer to re-apply effects when player revives
        addReviveObserver(player);

        // Start healing task if enabled
        startHealingTask(player);

        log.info("Player {} has been marked by the Devil", player.getName());
    }

    /**
     * Announce the marked player to the entire server
     */
    private void announceMarkedPlayer(Player player) {
        String message = String.format(
            "[Devil's Mark] %s has been marked by the Devil of Aion! Hunt them down for great rewards! " +
            "The mark will last for %d hours.",
            player.getName(), CustomConfig.DEVILS_MARK_DURATION_HOURS);

        // Send as regular message to all players
        World.getInstance().forEachPlayer(p -> {
            PacketSendUtility.sendMessage(p, message, ChatType.BRIGHT_YELLOW_CENTER);
        });
    }

    /**
     * Handle when a marked player is killed
     */
    public void onMarkedPlayerKilled(Player victim, Player killer) {
        log.info("DEBUG: onMarkedPlayerKilled called - victim: {}, killer: {}",
            victim != null ? victim.getName() : "null",
            killer != null ? killer.getName() : "null");

        if (currentMarkedPlayer == null) {
            log.info("DEBUG: No current marked player, ignoring kill");
            return;
        }

        log.info("DEBUG: Current marked player: {}", currentMarkedPlayer.getName());

        if (!currentMarkedPlayer.equals(victim)) {
            log.info("DEBUG: Victim {} is not the marked player {}, ignoring",
                victim.getName(), currentMarkedPlayer.getName());
            return;
        }

        log.info("DEBUG: Marked player {} was killed by {}, processing rewards",
            victim.getName(), killer.getName());

        // Reward the killer
        rewardKiller(killer);

        // Announce the kill
        announceMarkedPlayerKilled(victim, killer);

        // Unmark the player (they died) - but don't schedule next marking yet
        unmarkCurrentPlayerWithoutScheduling(false);

        // Immediately try to mark another player
        log.info("DEBUG: Marked player {} was killed, immediately selecting new marked player", victim.getName());
        selectAndMarkRandomPlayer();

        log.info("Marked player {} was killed by {}", victim.getName(), killer.getName());
    }

    /**
     * Handle when a marked player kills a normal player (reward the marked player)
     */
    public void onMarkedPlayerKill(Player killer, Player victim) {
        log.info("DEBUG: onMarkedPlayerKill called - killer: {}, victim: {}",
            killer != null ? killer.getName() : "null",
            victim != null ? victim.getName() : "null");

        if (currentMarkedPlayer == null) {
            log.info("DEBUG: No current marked player, ignoring kill");
            return;
        }

        log.info("DEBUG: Current marked player: {}", currentMarkedPlayer.getName());

        if (!currentMarkedPlayer.equals(killer)) {
            log.info("DEBUG: Killer {} is not the marked player {}, ignoring",
                killer.getName(), currentMarkedPlayer.getName());
            return;
        }

        log.info("DEBUG: Marked player {} killed normal player {}, processing rewards",
            killer.getName(), victim.getName());

        // Reward the marked player for their kill
        rewardMarkedPlayerKill(killer);

        // Announce the kill
        announceMarkedPlayerKill(killer, victim);

        log.info("Marked player {} killed normal player {}", killer.getName(), victim.getName());
    }

    /**
     * Reward the player who killed the marked player
     */
    private void rewardKiller(Player killer) {
        log.info("DEBUG: rewardKiller called for player: {}", killer.getName());

        // Give AP
        AbyssPointsService.addAp(killer, CustomConfig.DEVILS_MARK_KILLER_AP_REWARD);
        log.info("DEBUG: Gave {} AP to killer {}", CustomConfig.DEVILS_MARK_KILLER_AP_REWARD, killer.getName());

        // Give GP
        GloryPointsService.increaseGpBy(killer.getObjectId(), CustomConfig.DEVILS_MARK_KILLER_GP_REWARD);
        log.info("DEBUG: Gave {} GP to killer {}", CustomConfig.DEVILS_MARK_KILLER_GP_REWARD, killer.getName());

        // Give item rewards (multiple items supported)
        log.info("DEBUG: Giving items to killer {}: items={}, counts={}",
            killer.getName(), CustomConfig.DEVILS_MARK_KILLER_ITEMS, CustomConfig.DEVILS_MARK_KILLER_COUNTS);
        giveMultipleItems(killer, CustomConfig.DEVILS_MARK_KILLER_ITEMS, CustomConfig.DEVILS_MARK_KILLER_COUNTS);

        // Send reward message
        PacketSendUtility.sendMessage(killer,
            String.format("Devil's Mark Reward: +%d AP, +%d GP, and items for slaying the marked one!",
                CustomConfig.DEVILS_MARK_KILLER_AP_REWARD, CustomConfig.DEVILS_MARK_KILLER_GP_REWARD),
            ChatType.BRIGHT_YELLOW_CENTER);

        log.info("DEBUG: rewardKiller completed for player: {}", killer.getName());
    }

    /**
     * Reward the marked player for killing a normal player
     */
    private void rewardMarkedPlayerKill(Player markedPlayer) {
        log.info("DEBUG: rewardMarkedPlayerKill called for player: {}", markedPlayer.getName());

        // Give AP
        AbyssPointsService.addAp(markedPlayer, CustomConfig.DEVILS_MARK_MARKED_KILLER_AP_REWARD);
        log.info("DEBUG: Gave {} AP to marked player {}", CustomConfig.DEVILS_MARK_MARKED_KILLER_AP_REWARD, markedPlayer.getName());

        // Give GP
        GloryPointsService.increaseGpBy(markedPlayer.getObjectId(), CustomConfig.DEVILS_MARK_MARKED_KILLER_GP_REWARD);
        log.info("DEBUG: Gave {} GP to marked player {}", CustomConfig.DEVILS_MARK_MARKED_KILLER_GP_REWARD, markedPlayer.getName());

        // Give item rewards (multiple items supported)
        log.info("DEBUG: Giving items to marked player {}: items={}, counts={}",
            markedPlayer.getName(), CustomConfig.DEVILS_MARK_MARKED_KILLER_ITEMS, CustomConfig.DEVILS_MARK_MARKED_KILLER_COUNTS);
        giveMultipleItems(markedPlayer, CustomConfig.DEVILS_MARK_MARKED_KILLER_ITEMS, CustomConfig.DEVILS_MARK_MARKED_KILLER_COUNTS);

        // Send reward message
        PacketSendUtility.sendMessage(markedPlayer,
            String.format("Devil's Mark Kill Reward: +%d AP, +%d GP, and items for your kill!",
                CustomConfig.DEVILS_MARK_MARKED_KILLER_AP_REWARD, CustomConfig.DEVILS_MARK_MARKED_KILLER_GP_REWARD),
            ChatType.BRIGHT_YELLOW_CENTER);

        log.info("DEBUG: rewardMarkedPlayerKill completed for player: {}", markedPlayer.getName());
    }

    /**
     * Announce when a marked player is killed
     */
    private void announceMarkedPlayerKilled(Player victim, Player killer) {
        String message = String.format(
            "[Devil's Mark] %s has slain the marked %s! The Devil's Mark has been lifted.",
            killer.getName(), victim.getName());

        // Send as regular message to all players
        World.getInstance().forEachPlayer(p -> {
            PacketSendUtility.sendMessage(p, message, ChatType.BRIGHT_YELLOW_CENTER);
        });
    }

    /**
     * Announce when a marked player kills a normal player
     */
    private void announceMarkedPlayerKill(Player killer, Player victim) {
        String message = String.format(
            "[Devil's Mark] The marked %s has claimed %s! The hunt continues...",
            killer.getName(), victim.getName());

        // Send as regular message to all players
        World.getInstance().forEachPlayer(p -> {
            PacketSendUtility.sendMessage(p, message, ChatType.BRIGHT_YELLOW_CENTER);
        });
    }

    /**
     * Announce when a marked player logs out
     */
    private void announceMarkedPlayerLogout(Player player) {
        String message = String.format(
            "[Devil's Mark] %s has fled from the Devil's Mark by logging out! " +
            "The mark is being transferred to another player...",
            player.getName());

        // Send as regular message to all players
        World.getInstance().forEachPlayer(p -> {
            PacketSendUtility.sendMessage(p, message, ChatType.BRIGHT_YELLOW_CENTER);
        });
    }

    /**
     * Unmark the current player
     */
    private void unmarkCurrentPlayer(boolean survived) {
        if (currentMarkedPlayer == null) {
            return;
        }

        Player player = currentMarkedPlayer;

        // Remove the devil's mark state
        player.unsetCustomState(CustomPlayerState.DEVILS_MARK);

        // Also remove enemy of all players state
        player.unsetCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS);

        // Update player attributes to inform other players about the state change
        player.getController().onChangedPlayerAttributes();

        // Update protection status and broadcast to nearby players
        updateProtectionStatus(player);

        // Remove visual effects (if still applied)
        removeVisualEffects(player);

        if (survived) {
            // Player survived the full duration - give survival reward
            rewardSurvivor(player);
            announceSurvivor(player);
        }

        // Clear current marked player
        currentMarkedPlayer = null;

        // Cancel unmark task if it exists
        if (unmarkTask != null) {
            unmarkTask.cancel(false);
            unmarkTask = null;
        }

        // Remove revive observer
        removeReviveObserver(player);

        // Stop healing task
        stopHealingTask();

        // Schedule next marking
        scheduleNextMarking();

        log.info("Player {} is no longer marked (survived: {})", player.getName(), survived);
    }

    /**
     * Unmark the current player without scheduling the next marking (used when immediately selecting a new player)
     */
    private void unmarkCurrentPlayerWithoutScheduling(boolean survived) {
        if (currentMarkedPlayer == null) {
            return;
        }

        Player player = currentMarkedPlayer;

        // Remove the devil's mark state
        player.unsetCustomState(CustomPlayerState.DEVILS_MARK);

        // Also remove enemy of all players state
        player.unsetCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS);

        // Update player attributes to inform other players about the state change
        player.getController().onChangedPlayerAttributes();

        // Update protection status and broadcast to nearby players
        updateProtectionStatus(player);

        // Remove visual effects (if still applied)
        removeVisualEffects(player);

        if (survived) {
            // Player survived the full duration - give survival reward
            rewardSurvivor(player);
            announceSurvivor(player);
        }

        // Clear current marked player
        currentMarkedPlayer = null;

        // Cancel unmark task if it exists
        if (unmarkTask != null) {
            unmarkTask.cancel(false);
            unmarkTask = null;
        }

        // Remove revive observer
        removeReviveObserver(player);

        // Stop healing task
        stopHealingTask();

        // NOTE: We don't call scheduleNextMarking() here - that's the difference from unmarkCurrentPlayer()

        log.info("Player {} is no longer marked (survived: {}) - ready for immediate new selection", player.getName(), survived);
    }

    /**
     * Unmark the current player for logout (no rewards, no next marking schedule)
     */
    private void unmarkCurrentPlayerForLogout(Player player) {
        if (currentMarkedPlayer == null || !currentMarkedPlayer.equals(player)) {
            return;
        }

        // Remove the devil's mark state
        player.unsetCustomState(CustomPlayerState.DEVILS_MARK);

        // Also remove enemy of all players state
        player.unsetCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS);

        // Update player attributes to inform other players about the state change
        player.getController().onChangedPlayerAttributes();

        // Update protection status and broadcast to nearby players
        updateProtectionStatus(player);

        // Remove visual effects (if still applied)
        removeVisualEffects(player);

        // Clear current marked player
        currentMarkedPlayer = null;

        // Cancel unmark task if it exists
        if (unmarkTask != null) {
            unmarkTask.cancel(false);
            unmarkTask = null;
        }

        // Remove revive observer
        removeReviveObserver(player);

        // Stop healing task
        stopHealingTask();

        // Note: We don't schedule next marking here as it will be done immediately
        // Note: We don't give survival rewards since the player logged out

        log.info("Player {} mark removed due to logout", player.getName());
    }

    /**
     * Reward a player who survived being marked
     */
    private void rewardSurvivor(Player player) {
        log.info("DEBUG: rewardSurvivor called for player: {}", player.getName());

        // Give survival AP
        AbyssPointsService.addAp(player, CustomConfig.DEVILS_MARK_SURVIVAL_AP_REWARD);
        log.info("DEBUG: Gave {} survival AP to {}", CustomConfig.DEVILS_MARK_SURVIVAL_AP_REWARD, player.getName());

        // Give survival items (multiple items supported)
        log.info("DEBUG: Giving survival items to {}: items={}, counts={}",
            player.getName(), CustomConfig.DEVILS_MARK_SURVIVAL_ITEMS, CustomConfig.DEVILS_MARK_SURVIVAL_COUNTS);
        giveMultipleItems(player, CustomConfig.DEVILS_MARK_SURVIVAL_ITEMS, CustomConfig.DEVILS_MARK_SURVIVAL_COUNTS);

        // Send reward message
        PacketSendUtility.sendMessage(player,
            String.format("Devil's Mark Survival Reward: +%d AP and items for surviving the hunt!",
                CustomConfig.DEVILS_MARK_SURVIVAL_AP_REWARD),
            ChatType.BRIGHT_YELLOW_CENTER);

        log.info("DEBUG: rewardSurvivor completed for player: {}", player.getName());
    }

    /**
     * Announce when a marked player survives
     */
    private void announceSurvivor(Player player) {
        String message = String.format(
            "[Devil's Mark] %s has survived the Devil's Mark for %d hours! The mark has been lifted.",
            player.getName(), CustomConfig.DEVILS_MARK_DURATION_HOURS);

        // Send as regular message to all players
        World.getInstance().forEachPlayer(p -> {
            PacketSendUtility.sendMessage(p, message, ChatType.BRIGHT_YELLOW_CENTER);
        });
    }

    /**
     * Manually mark a specific player (for admin use)
     */
    public boolean manuallyMarkPlayer(Player player) {
        if (!CustomConfig.DEVILS_MARK_ENABLED) {
            return false;
        }

        if (player == null || !player.isOnline()) {
            return false;
        }

        // Check if player is eligible
        if (!isEligibleForMarking(player)) {
            return false;
        }

        // Mark the player
        markPlayer(player);
        return true;
    }

    /**
     * Manually unmark the current player (for admin use)
     */
    public boolean manuallyUnmarkPlayer() {
        if (currentMarkedPlayer == null) {
            return false;
        }

        unmarkCurrentPlayer(false);
        return true;
    }

    /**
     * Check if a player is currently marked
     */
    public boolean isMarked(Player player) {
        return currentMarkedPlayer != null && currentMarkedPlayer.equals(player);
    }

    /**
     * Handle player logout - transfer mark if the marked player logs out
     */
    public void onPlayerLogout(Player player) {
        if (!CustomConfig.DEVILS_MARK_ENABLED) {
            return;
        }

        // Check if the logging out player is the currently marked player
        if (currentMarkedPlayer != null && currentMarkedPlayer.equals(player)) {
            log.info("Marked player {} is logging out, transferring mark to another player", player.getName());

            // Announce that the marked player logged out
            announceMarkedPlayerLogout(player);

            // Remove mark from current player (without rewards since they logged out)
            unmarkCurrentPlayerForLogout(player);

            // Immediately try to mark another player
            selectAndMarkRandomPlayer();
        }
    }

    /**
     * Get current Devil's Mark status for debugging
     */
    public String getDebugStatus() {
        StringBuilder sb = new StringBuilder();
        sb.append("=== Devil's Mark Debug Status ===\n");
        sb.append("System enabled: ").append(CustomConfig.DEVILS_MARK_ENABLED).append("\n");
        sb.append("Current marked player: ").append(currentMarkedPlayer != null ? currentMarkedPlayer.getName() : "None").append("\n");
        sb.append("Mark start time: ").append(markStartTime).append("\n");
        sb.append("Duration hours: ").append(CustomConfig.DEVILS_MARK_DURATION_HOURS).append("\n");
        sb.append("Killer AP reward: ").append(CustomConfig.DEVILS_MARK_KILLER_AP_REWARD).append("\n");
        sb.append("Killer GP reward: ").append(CustomConfig.DEVILS_MARK_KILLER_GP_REWARD).append("\n");
        sb.append("Killer items: ").append(CustomConfig.DEVILS_MARK_KILLER_ITEMS).append("\n");
        sb.append("Killer counts: ").append(CustomConfig.DEVILS_MARK_KILLER_COUNTS).append("\n");
        sb.append("Survival AP reward: ").append(CustomConfig.DEVILS_MARK_SURVIVAL_AP_REWARD).append("\n");
        sb.append("Survival items: ").append(CustomConfig.DEVILS_MARK_SURVIVAL_ITEMS).append("\n");
        sb.append("Survival counts: ").append(CustomConfig.DEVILS_MARK_SURVIVAL_COUNTS).append("\n");
        sb.append("Cross faction targeting: ").append(CustomConfig.DEVILS_MARK_CROSS_FACTION_TARGETING).append("\n");

        if (currentMarkedPlayer != null) {
            long timeElapsed = System.currentTimeMillis() - markStartTime;
            long timeRemaining = (CustomConfig.DEVILS_MARK_DURATION_HOURS * 60 * 60 * 1000) - timeElapsed;
            sb.append("Time elapsed: ").append(timeElapsed / 1000 / 60).append(" minutes\n");
            sb.append("Time remaining: ").append(timeRemaining / 1000 / 60).append(" minutes\n");
        }

        return sb.toString();
    }

    /**
     * Update protection status and broadcast to nearby players
     */
    private void updateProtectionStatus(Player player) {
        // Send updated player state to nearby players to reflect protection changes
        PacketSendUtility.broadcastToSightedPlayers(player,
            new com.aionemu.gameserver.network.aion.serverpackets.SM_PLAYER_STATE(player), true);
    }

    /**
     * Get the currently marked player
     */
    public Player getCurrentMarkedPlayer() {
        return currentMarkedPlayer;
    }

    /**
     * Get time remaining for current mark (in milliseconds)
     */
    public long getTimeRemaining() {
        if (currentMarkedPlayer == null) {
            return 0;
        }

        long elapsed = System.currentTimeMillis() - markStartTime;
        int durationMs = CustomConfig.DEVILS_MARK_DURATION_HOURS * 60 * 60 * 1000;
        return Math.max(0, durationMs - elapsed);
    }

    /**
     * Shutdown the service
     */
    public void shutdown() {
        if (markTask != null) {
            markTask.cancel(false);
        }
        if (unmarkTask != null) {
            unmarkTask.cancel(false);
        }

        // Stop healing task
        stopHealingTask();

        if (currentMarkedPlayer != null) {
            unmarkCurrentPlayer(false);
        }

        log.info("Devil's Mark service shutdown complete.");
    }

    /**
     * Start the healing task for the marked player
     */
    private void startHealingTask(Player player) {
        if (!CustomConfig.DEVILS_MARK_HEALING_ENABLE) {
            return;
        }

        // Stop any existing healing task
        stopHealingTask();

        // Schedule healing every X seconds
        int intervalMs = CustomConfig.DEVILS_MARK_HEALING_INTERVAL * 1000;
        healingTask = ThreadPoolManager.getInstance().scheduleAtFixedRate(() -> {
            if (currentMarkedPlayer != null &&
                currentMarkedPlayer.equals(player) &&
                currentMarkedPlayer.isOnline() &&
                !currentMarkedPlayer.isDead() &&
                currentMarkedPlayer.isInCustomState(CustomPlayerState.DEVILS_MARK)) {

                applyHealingSkills(currentMarkedPlayer);
            }
        }, intervalMs, intervalMs);

        log.info("Started healing task for marked player {} (interval: {}s)",
            player.getName(), CustomConfig.DEVILS_MARK_HEALING_INTERVAL);
    }

    /**
     * Stop the healing task
     */
    private void stopHealingTask() {
        if (healingTask != null) {
            healingTask.cancel(false);
            healingTask = null;
            log.debug("Stopped healing task for Devil's Mark");
        }
    }

    /**
     * Apply healing skills to the marked player
     */
    private void applyHealingSkills(Player player) {
        String[] skillIds = CustomConfig.DEVILS_MARK_HEALING_SKILLS.split(",");

        for (String skillIdStr : skillIds) {
            try {
                int skillId = Integer.parseInt(skillIdStr.trim());

                // Check if skill template exists
                com.aionemu.gameserver.skillengine.model.SkillTemplate skillTemplate =
                    com.aionemu.gameserver.dataholders.DataManager.SKILL_DATA.getSkillTemplate(skillId);

                if (skillTemplate == null) {
                    log.warn("Devil's Mark healing skill {} not found in skill templates", skillId);
                    continue;
                }

                // Use the player's controller to cast the skill (this handles all the proper checks and casting)
                boolean success = player.getController().useSkill(skillId, skillTemplate.getLvl());

                if (success) {
                    log.debug("Cast Devil's Mark healing skill {} on player {}", skillId, player.getName());

                    // Send message to player about the healing
                    PacketSendUtility.sendMessage(player,
                        "The Devil's Mark grants you dark regeneration...",
                        ChatType.BRIGHT_YELLOW_CENTER);
                } else {
                    log.debug("Failed to cast Devil's Mark healing skill {} on player {} (skill conditions not met)",
                        skillId, player.getName());
                }

            } catch (Exception e) {
                log.warn("Failed to cast Devil's Mark healing skill {} on player {}: {}",
                    skillIdStr.trim(), player.getName(), e.getMessage());
            }
        }
    }

    /**
     * Apply multiple visual effects to the marked player
     */
    private void applyVisualEffects(Player player) {
        String[] effectIds = CustomConfig.DEVILS_MARK_VISUAL_EFFECTS.split(",");
        int duration = CustomConfig.DEVILS_MARK_DURATION_HOURS * 60 * 60 * 1000;

        for (String effectIdStr : effectIds) {
            try {
                int effectId = Integer.parseInt(effectIdStr.trim());
                SkillEngine.getInstance().applyEffectDirectly(
                    effectId,
                    player,
                    player,
                    duration,
                    null
                );
                log.debug("Applied Devil's Mark visual effect {} to player {}", effectId, player.getName());
            } catch (Exception e) {
                log.warn("Failed to apply Devil's Mark visual effect {} to player {}: {}",
                    effectIdStr.trim(), player.getName(), e.getMessage());
            }
        }

        // After applying effects, ensure player can still attack by removing problematic abnormal states
        ThreadPoolManager.getInstance().schedule(() -> {
            if (player.isOnline() && player.isInCustomState(CustomPlayerState.DEVILS_MARK)) {
                removeAttackBlockingStates(player);
            }
        }, 1000); // Small delay to let effects apply first
    }

    /**
     * Remove abnormal states that prevent attacking while keeping visual effects
     */
    private void removeAttackBlockingStates(Player player) {
        // Remove specific abnormal states that block attacking
        player.getEffectController().unsetAbnormal(
            com.aionemu.gameserver.skillengine.effect.AbnormalState.FEAR);
        player.getEffectController().unsetAbnormal(
            com.aionemu.gameserver.skillengine.effect.AbnormalState.STUN);
        player.getEffectController().unsetAbnormal(
            com.aionemu.gameserver.skillengine.effect.AbnormalState.PARALYZE);
        player.getEffectController().unsetAbnormal(
            com.aionemu.gameserver.skillengine.effect.AbnormalState.SLEEP);
        player.getEffectController().unsetAbnormal(
            com.aionemu.gameserver.skillengine.effect.AbnormalState.STUMBLE);
        player.getEffectController().unsetAbnormal(
            com.aionemu.gameserver.skillengine.effect.AbnormalState.STAGGER);
        player.getEffectController().unsetAbnormal(
            com.aionemu.gameserver.skillengine.effect.AbnormalState.CONFUSE);
        player.getEffectController().unsetAbnormal(
            com.aionemu.gameserver.skillengine.effect.AbnormalState.SANCTUARY);

        log.debug("Removed attack-blocking states from Devil's Mark player {}", player.getName());
    }

    /**
     * Remove visual effects from the player
     */
    private void removeVisualEffects(Player player) {
        String[] effectIds = CustomConfig.DEVILS_MARK_VISUAL_EFFECTS.split(",");

        for (String effectIdStr : effectIds) {
            try {
                int effectId = Integer.parseInt(effectIdStr.trim());
                player.getEffectController().removeEffect(effectId);
                log.debug("Removed Devil's Mark visual effect {} from player {}", effectId, player.getName());
            } catch (Exception e) {
                log.debug("Could not remove Devil's Mark visual effect {} from player {}: {} (effect may have already expired)",
                    effectIdStr.trim(), player.getName(), e.getMessage());
            }
        }
    }

    /**
     * Give multiple items to a player based on comma-separated configuration
     */
    private void giveMultipleItems(Player player, String itemIds, String itemCounts) {
        log.info("DEBUG: giveMultipleItems called for player: {}, itemIds: {}, itemCounts: {}",
            player.getName(), itemIds, itemCounts);

        String[] ids = itemIds.split(",");
        String[] counts = itemCounts.split(",");

        for (int i = 0; i < ids.length; i++) {
            try {
                int itemId = Integer.parseInt(ids[i].trim());
                int count = (i < counts.length) ? Integer.parseInt(counts[i].trim()) : 1;

                log.info("DEBUG: Processing item {} x{} for player {}", itemId, count, player.getName());

                // Check if item template exists
                com.aionemu.gameserver.model.templates.item.ItemTemplate itemTemplate =
                    com.aionemu.gameserver.dataholders.DataManager.ITEM_DATA.getItemTemplate(itemId);

                if (itemTemplate == null) {
                    log.warn("DEBUG: Item template {} not found, skipping", itemId);
                    continue;
                }

                log.info("DEBUG: Sending express mail to {} for item {} x{}", player.getName(), itemId, count);

                // Always send Devil's Mark rewards via express mail
                boolean mailSent = com.aionemu.gameserver.services.mail.SystemMailService.sendMail(
                    "$$SYSTEM_MAIL",
                    player.getName(),
                    "Devil's Mark Reward",
                    "Congratulations! You have received Devil's Mark rewards. These valuable items have been delivered via express mail.",
                    itemId,
                    count,
                    0,
                    com.aionemu.gameserver.model.gameobjects.LetterType.EXPRESS
                );

                if (!mailSent) {
                    log.warn("Failed to send express mail to player {} for item {} x{}", player.getName(), itemId, count);
                } else {
                    log.info("DEBUG: Successfully sent express mail to {} for item {} x{}", player.getName(), itemId, count);
                }
            } catch (Exception e) {
                log.warn("Failed to give item {} to player {}: {}",
                    ids[i].trim(), player.getName(), e.getMessage());
                e.printStackTrace();
            }
        }
    }

    /**
     * Add observer to re-apply visual effects when marked player revives
     */
    private void addReviveObserver(Player player) {
        // Remove existing observer if any
        removeReviveObserver(player);

        reviveObserver = new ActionObserver(ObserverType.HP_CHANGED) {
            private boolean wasDead = false;

            @Override
            public void hpChanged(int value) {
                // Check if player was dead and now has HP (revival)
                if (currentMarkedPlayer != null &&
                    currentMarkedPlayer.equals(player) &&
                    currentMarkedPlayer.isInCustomState(CustomPlayerState.DEVILS_MARK)) {

                    boolean isDead = currentMarkedPlayer.isDead();

                    // If player was dead and now is alive, they revived
                    if (wasDead && !isDead && currentMarkedPlayer.getLifeStats().getCurrentHp() > 0) {
                        // Re-apply visual effects after a short delay
                        ThreadPoolManager.getInstance().schedule(() -> {
                            if (currentMarkedPlayer.isOnline() &&
                                currentMarkedPlayer.isInCustomState(CustomPlayerState.DEVILS_MARK) &&
                                !currentMarkedPlayer.isDead()) {

                                applyVisualEffects(currentMarkedPlayer);

                                // Send message to player
                                PacketSendUtility.sendMessage(currentMarkedPlayer,
                                    "The Devil's Mark has been restored! You are still being hunted!",
                                    ChatType.BRIGHT_YELLOW_CENTER);

                                log.info("Re-applied Devil's Mark effects to revived player {}",
                                    currentMarkedPlayer.getName());
                            }
                        }, 3000); // 3-second delay to ensure player is fully revived
                    }

                    wasDead = isDead;
                }
            }
        };

        player.getObserveController().addObserver(reviveObserver);
        log.debug("Added revive observer for Devil's Mark player {}", player.getName());
    }

    /**
     * Remove revive observer from player
     */
    private void removeReviveObserver(Player player) {
        if (reviveObserver != null && player != null) {
            player.getObserveController().removeObserver(reviveObserver);
            reviveObserver = null;
            log.debug("Removed revive observer from player {}", player.getName());
        }
    }
}
