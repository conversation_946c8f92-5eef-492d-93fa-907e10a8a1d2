package com.aionemu.gameserver.skillengine.effect;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;

/**
 * <AUTHOR>
 */
@XmlType(name = "EffectType")
@XmlEnum
public enum EffectType {

	ABS<PERSON>UTEEXPPOINTHEALINSTANT,
	ABS<PERSON><PERSON><PERSON><PERSON><PERSON>,
	ABSOL<PERSON><PERSON><PERSON><PERSON>,
	ABS<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>BUFF,
	ABSOLUTES<PERSON><PERSON><PERSON>CDEBUFF,
	ACT<PERSON><PERSON><PERSON><PERSON><PERSON>AV<PERSON>,
	ALWAYSBLOCK,
	AL<PERSON><PERSON><PERSON>OD<PERSON>,
	ALWAYSHIT,
	ALWAYSNORESIST,
	ALWAYSPARRY,
	ALWAYSRESIST,
	APBOOST,
	ARMORMASTERY,
	AURA,
	BACKDASH,
	<PERSON>IND,
	<PERSON><PERSON>ED,
	BLIND,
	BOOSTDROPRATE,
	BOOSTHATE,
	B<PERSON>OS<PERSON><PERSON>L,
	B<PERSON>OSTSK<PERSON>LCASTINGTIME,
	BOOSTSKILLCOST,
	BOOSTSPELLATTACK,
	BUFFBIND,
	BUFFSILENCE,
	BUFFSLEEP,
	B<PERSON>FFSTUN,
	CANNON,
	CARVESIGNET,
	CASEH<PERSON>L,
	CH<PERSON><PERSON><PERSON><PERSON><PERSON>ATTACKED,
	CLOSEAERIAL,
	CO<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ER,
	CONFUSE,
	CONVERTHEAL,
	CURSE,
	DASH,
	DEATHBLOW,
	DEBOOSTHEAL,
	DEFORM,
	DELAYEDFPATKINSTANT,
	DELAYEDSKILL,
	DELAYEDSPELLATTACKINSTANT,
	DISEASE,
	DISPEL,
	DISPELBUFF,
	DISPELBUFFCOUNTERATK,
	DISPELDEBUFF,
	DISPELDEBUFFMENTAL,
	DISPELDEBUFFPHYSICAL,
	DISPELNPCBUFF,
	DISPELNPCDEBUFF,
	DPHEAL,
	DPHEALINSTANT,
	DPTRANSFER,
	DRBOOST,
	DUMMY,
	ESCAPE,
	EVADE,
	EXTENDAURARANGE,
	FALL,
	FEAR,
	FLYOFF,
	FPATTACK,
	FPATTACKINSTANT,
	FPHEAL,
	FPHEALINSTANT,
	HEAL,
	HEALCASTORONATTACKED,
	HEALCASTORONTARGETDEAD,
	HEALINSTANT,
	HIDE,
	HIPASS,
	HOSTILEUP,
	INTERVALSKILL,
	INVULNERABLEWING,
	LIMITEDREDUCEDAMAGE,
	MAGICCOUNTERATK,
	MOVEBEHIND,
	MPATTACK,
	MPATTACKINSTANT,
	MPHEAL,
	MPHEALINSTANT,
	NODEATHPENALTY,
	NOFLY,
	NOREDUCESPELLATKINSTANT,
	NORESURRECTPENALTY,
	ONETIMEBOOSTHEAL,
	ONETIMEBOOSTSKILLATTACK,
	ONETIMEBOOSTSKILLCRITICAL,
	OPENAERIAL,
	PARALYZE,
	PETORDERUNSUMMON,
	PETORDERUSEULTRASKILL,
	PETRIFICATION,
	POISON,
	POLYMORPH,
	PROCATKINSTANT,
	PROCDPHEALINSTANT,
	PROCFPHEALINSTANT,
	PROCHEALINSTANT,
	PROCMPHEALINSTANT,
	PROCVPHEALINSTANT,
	PROTECT,
	PROVOKER,
	PULLED,
	RANDOMMOVELOC,
	REBIRTH,
	RECALLINSTANT,
	REFLECTOR,
	RESURRECT,
	RESURRECTBASE,
	RESURRECTPOSITIONAL,
	RETURN,
	RETURNPOINT,
	RIDEROBOT,
	ROOT,
	SANCTUARY,
	SEARCH,
	SHAPECHANGE,
	SHIELD,
	MPSHIELD,
	SHIELDMASTERY,
	SIGNET,
	SIGNETBURST,
	SILENCE,
	SIMPLEROOT,
	SKILLATKDRAININSTANT,
	SKILLATTACKINSTANT,
	SKILLCOOLTIMERESET,
	SKILLLAUNCHER,
	SKILLXPBOOST,
	SLEEP,
	SLOW,
	SNARE,
	SPELLATTACK,
	SPELLATKDRAIN,
	SPELLATKDRAININSTANT,
	SPELLATTACKINSTANT,
	SPIN,
	STAGGER,
	STATBOOST,
	STATDOWN,
	STATUP,
	STUMBLE,
	STUN,
	SUBTYPEBOOSTRESIST,
	SUBTYPEEXTENDDURATION,
	SUMMON,
	SUMMONBINDINGGROUPGATE,
	SUMMONFUNCTIONALNPC,
	SUMMONGROUPGATE,
	SUMMONHOMING,
	SUMMONHOUSEGATE,
	SUMMONSERVANT,
	SUMMONSKILLAREA,
	SUMMONTOTEM,
	SUMMONTRAP,
	SUPPORTEVENT,
	SWITCHHOSTILE,
	SWITCHHPMP,
	TARGETCHANGE,
	TARGETTELEPORT,
	UTILITY,
	WEAPONSTATBOOST,
	WEAPONSTATUP,
	WEAPONDUAL,
	WEAPONMASTERY,
	XPBOOST;
}
