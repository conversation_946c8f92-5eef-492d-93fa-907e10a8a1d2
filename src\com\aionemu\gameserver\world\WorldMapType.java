package com.aionemu.gameserver.world;

public enum WorldMapType {
	// Asmodae
	PANDAEMONIUM(120010000),
	MARCHUTAN(120020000),
	ISHALGEN(220010000),
	MORHEIM(220020000),
	ALTGARD(220030000),
	BELUSLAN(220040000),
	BRUSTHONIN(220050000),

	// Elysea
	SANCTUM(110010000),
	KAISINEL(110020000),
	POETA(210010000),
	ELTNEN(210020000),
	VERTERON(210030000),
	HEIRON(210040000),
	THEOBOMOS(210060000),

	// <PERSON><PERSON>rea
	INGGISON(210050000),
	GELKMAROS(220070000),
	S<PERSON><PERSON><PERSON><PERSON>_CANYON(600010000),

	// Prison
	LF_PRISON(510010000), // For ELYOS
	DF_PRISON(520010000), // For ASMODIANS

	RESHANTA(400010000),

	// Instances
	NO_ZONE_NAME(300010000),
	ID_TEST_DUNGEON(300020000),
	NOCHSANA_TRAINING_CAMP(300030000),
	DARK_POE<PERSON>(300040000),
	ASTERIA_CHAMBER(300050000),
	SULFUR_TREE_NEST(300060000),
	CHAMBER_OF_ROAH(300070000),
	LEFT_WING_CHAMBER(300080000),
	RIGHT_WING_CHAMBER(300090000),
	STEEL_RAKE(300100000),
	DREDGION(300110000),
	KYSIS_CHAMBER(300120000),
	MIREN_CHAMBER(300130000),
	KROTAN_CHAMBER(300140000),
	UDAS_TEMPLE(300150000),
	UDAS_TEMPLE_LOWER(300160000),
	BESHMUNDIR_TEMPLE(300170000),
	TALOCS_HOLLOW(*********),
	HARAMEL(*********),
	DREDGION_OF_CHANTRA(*********),
	ABYSSAL_SPLINTER(*********),
	KROMEDES_TRIAL(*********),
	KARAMATIS(*********),
	KARAMATIS_B(*********),
	AERDINA(*********),
	GERANAIA(*********),
	AETHEROGENETICS_LAB(*********),
	FRAGMENT_OF_DARKNESS(*********),
	IDLF1B_STIGMA(*********),
	SANCTUM_UNDERGROUND_ARENA(*********),
	TRINIEL_UNDERGROUND_ARENA(*********),
	INDRATU_FORTRESS(*********),
	AZOTURAN_FORTRESS(*********),
	THEOBOMOS_LAB(*********),
	IDAB_PRO_L3(*********),
	ATAXIAR(*********),
	ATAXIAR_B(*********),
	BREGIRUN(*********),
	NIDALBER(*********),
	ARKANIS_TEMPLE(*********),
	SPACE_OF_OBLIVION(*********),
	SPACE_OF_DESTINY(*********),
	DRAUPNIR_CAVE(*********),
	FIRE_TEMPLE(*********),
	ALQUIMIA_RESEARCH_CENTER(*********),
	SHADOW_COURT_DUNGEON(*********),
	ADMA_STRONGHOLD(*********),
	IDAB_PRO_D3(*********),

	// Maps 2.5
	KAISINEL_ACADEMY(*********),
	MARCHUTAN_PRIORY(*********),
	ESOTERRACE(*********),
	EMPYREAN_CRUCIBLE(*********),

	// Map 2.6
	CRUCIBLE_CHALLENGE(*********),

	// Maps 2.7
	ARENA_OF_CHAOS(*********),
	ARENA_OF_DISCIPLINE(*********),
	CHAOS_TRAINING_GROUNDS(*********),
	DISCIPLINE_TRAINING_GROUNDS(*********),
	PADMARASHKA_CAVE(*********),

	// Test Map
	TEST_BASIC(*********),
	TEST_SERVER(*********),
	TEST_GIANTMONSTER(*********),
	HOUSING_BARRACK(*********),
	Test_IDArena(*********),
	IDLDF5RE_test(*********),
	Test_Kgw(*********),
	Test_Basic_Mj(*********),
	test_intro(*********),
	Test_server_art(*********),
	Test_TagMatch(*********),
	test_timeattack(900200000),
	System_Basic(900220000),

	// Maps 3.0

	// Instances
	RAKSANG(300310000),
	RENTUS_BASE(300280000),
	ATURAM_SKY_FORTRESS(300240000),
	STEEL_RAKE_CABIN(300460000),
	TERATH_DREDGION(300440000),

	// Map 3.5
	ARENA_OF_HARMONY(300450000),
	TIAMAT_STRONGHOLD(300510000),
	DRAGON_LORDS_REFUGE(300520000),
	ARENA_OF_GLORY(300550000),
	SHUGO_IMPERIAL_TOMB(300560000),
	HARMONY_TRAINING_GROUNDS(300570000),
	UNSTABLE_SPLINTER(300600000), // Unstable Abyssal Splinter
	HEXWAY(300700000),

	// Instances 4.3 NA
	SEALED_DANUAR_MYSTICARIUM(300480000),
	ETERNAL_BASTION(300540000),
	OPHIDAN_BRIDGE(300590000),
	INFINITY_SHARD(300800000),
	DANUAR_RELIQUARY(301110000),
	KAMAR_BATTLEFIELD(301120000),
	SAURO_SUPPLY_BASE(301130000),
	SEIZED_DANUAR_SANCTUARY(301140000),
	NIGHTMARE_CIRCUS(301160000),
	THE_NIGHTMARE_CIRCUS(301200000),

	// Maps 4.3 NA
	LIFE_PARTY_CONCERT_HALL(600080000),

	// Maps 4.5 NA + Instances
	ENGULFED_OPHIDAN_BRIDGE(301210000),
	IRON_WALL_WARFRONT(301220000),
	ILLUMINARY_OBELISK(301230000),
	LEGIONS_KYSIS_BARRACKS(301240000),
	LEGIONS_MIREN_BARRACKS(301250000),
	LEGIONS_KROTAN_BARRACKS(301260000),
	LINKGATE_FOUNDRY(301270000),
	KYSIS_BARRACKS(301280000),
	MIREN_BARRACKS(301290000),
	KROTAN_BARRACKS(301300000),
	IDGEL_DOME(301310000),
	LUCKY_OPHIDAN_BRIDGE(301320000),
	LUCKY_DANUAR_RELIQUARY(301330000),

	// Maps 4.7 NA + Instances
	QUEST_LINKGATE_FOUNDRY(301340000),
	INFERNAL_DANUAR_RELIQUARY(301360000),
	INFERNAL_ILLUMINARY_OBELISK(301370000),
	BELUS(400020000),
	TRANSIDIUM_ANNEX(400030000),
	ASPIDA(400040000),
	ATANATOS(400050000),
	DISILLON(400060000),
	KALDOR(600090000),
	LEVINSHOR(600100000),

	// Housing
	ORIEL(700010000),
	PERNON(710010000),

	// Test Maps 4.7
	Test_MRT_IDZone(300290000),

	// Maps 4.7.5.0
	THE_SHUGO_EMPERORS_VAULT(301400000),
	WISPLIGHT_ABBEY(130090000),
	FATEBOUND_ABBEY(140010000),

	// Maps 4.8
	IDIAN_DEPTHS_DARK(220100000),
	CYGNEA(210070000),
	GRIFFOEN(210080000),
	IDIAN_DEPTHS_LIGHT(210090000),
	ENSHAR(220080000),
	HABROK(220090000),

	// Instances 4.8
	RAKSANG_RUINS(300610000),
	OCCUPIED_RENTUS_BASE(300620000),
	ANGUISHED_DRAGON_LORDS_REFUGE(300630000),
	DANUAR_SANCTUARY(301380000),
	DRAKENSPIRE_DEPHTS(301390000),
	STONESPEAR_REACH(301500000),

	// Housing
	HOUSING_LC_LEGION(700020000, true),
	HOUSING_DC_LEGION(710020000, true),
	HOUSING_IDLF_PERSONAL(720010000, true),
	HOUSING_IDDF_PERSONAL(730010000, true);

	private final int worldId;
	private final boolean isPersonal;

	WorldMapType(int worldId) {
		this(worldId, false);
	}

	WorldMapType(int worldId, boolean personal) {
		this.worldId = worldId;
		this.isPersonal = personal;
	}

	public int getId() {
		return worldId;
	}

	public boolean isPersonal() {
		return isPersonal;
	}

	/**
	 * @param id
	 *          of world
	 * @return WorldMapType
	 */
	public static WorldMapType getWorld(int id) {
		for (WorldMapType type : values()) {
			if (type.getId() == id)
				return type;
		}
		return null;
	}

	public static WorldMapType of(String worldName) {
		worldName = worldName.toLowerCase().replace(" ", "_");
		for (WorldMapType type : values())
			if (type.name().toLowerCase().equals(worldName))
				return type;
		return null;
	}

	public static int getMapId(String worldName) {
		WorldMapType worldMapType = of(worldName);
		return worldMapType == null ? 0 : worldMapType.getId();
	}

	public static boolean isPanesterraMap(int id) {
		return switch (getWorld(id)) {
			case BELUS, TRANSIDIUM_ANNEX, ASPIDA, ATANATOS, DISILLON -> true;
			case null, default -> false;
		};
	}
}
