package com.aionemu.gameserver.skillengine.effect;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;

import com.aionemu.gameserver.skillengine.model.Effect;

/**
 * <AUTHOR>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AbsoluteEXPPointHealInstantEffect")
public class AbsoluteEXPPointHealInstantEffect extends EffectTemplate {

	@Override
	public void applyEffect(Effect effect) {
		// TODO Auto-generated method stub

	}

}
