package com.aionemu.gameserver.questEngine.task.checker;

import com.aionemu.gameserver.model.gameobjects.Creature;

/**
 * <AUTHOR> Neon
 */
public abstract class DestinationChecker {

	protected final Creature follower;

	Destination<PERSON><PERSON><PERSON>(Creature follower) {
		this.follower = follower;
	}

	public Creature getFollower() {
		return follower;
	}

	public abstract boolean check();
}
