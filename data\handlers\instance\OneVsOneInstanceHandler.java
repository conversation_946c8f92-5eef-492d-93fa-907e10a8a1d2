package instance;

import com.aionemu.gameserver.instance.handlers.GeneralInstanceHandler;
import com.aionemu.gameserver.instance.handlers.InstanceID;
import com.aionemu.gameserver.model.gameobjects.Creature;
import com.aionemu.gameserver.model.gameobjects.player.Player;
import com.aionemu.gameserver.services.OneVsOneService;
import com.aionemu.gameserver.services.player.PlayerReviveService;
import com.aionemu.gameserver.world.WorldMapInstance;

/**
 * OneVsOne Instance Handler for map 320090000
 * Handles death mechanics, player restrictions, and proper cleanup for OneVsOne matches
 * 
 * <AUTHOR> System
 */
@InstanceID(320090000)
public class OneVsOneInstanceHandler extends GeneralInstanceHandler {

    public OneVsOneInstanceHandler(WorldMapInstance instance) {
        super(instance);
    }

    @Override
    public void onEnterInstance(Player player) {
        super.onEnterInstance(player);
        // Player entered OneVsOne instance
    }

    @Override
    public void onLeaveInstance(Player player) {
        super.onLeaveInstance(player);
        
        // Clean up player from OneVsOne system when they leave
        if (OneVsOneService.getInstance().isPlayerInActiveMatch(player)) {
            OneVsOneService.getInstance().handlePlayerLeave(player);
        }
    }

    @Override
    public boolean onDie(Player player, Creature lastAttacker) {
        // Check OneVsOne match participation
        if (OneVsOneService.getInstance().isPlayerInActiveMatch(player)) {
            // Always immediately resurrect players in OneVsOne matches to prevent resurrection dialog
            PlayerReviveService.revive(player, 100, 100, false, 0);
            player.getGameStats().updateStatsAndSpeedVisually();
            player.unsetResPosState();
            player.setPlayerResActivate(false);

            // Handle player death in OneVsOne match after resurrection
            if (lastAttacker instanceof Player) {
                OneVsOneService.getInstance().onPlayerKill((Player) lastAttacker, player);
            }
            return true; // Prevent default death handling and resurrection dialog
        }

        // Default death handling for non-OneVsOne situations
        return super.onDie(player, lastAttacker);
    }

    @Override
    public boolean allowSelfReviveBySkill() {
        // Disable self-revive skills in OneVsOne to maintain fair play
        return false;
    }

    @Override
    public boolean allowSelfReviveByItem() {
        // Disable self-revive items in OneVsOne to maintain fair play
        return false;
    }

    @Override
    public boolean allowKiskRevive() {
        // Disable kisk revival in OneVsOne
        return false;
    }

    @Override
    public boolean allowInstanceRevive() {
        // Allow instance revival (handled by OneVsOne system)
        return true;
    }
}
