package com.aionemu.gameserver.network.aion.serverpackets;

import java.util.Map;

import com.aionemu.gameserver.model.items.ItemCooldown;
import com.aionemu.gameserver.network.aion.AionConnection;
import com.aionemu.gameserver.network.aion.AionServerPacket;

/**
 * <AUTHOR>
 */
public class SM_ITEM_COOLDOWN extends AionServerPacket {

	private Map<Integer, ItemCooldown> cooldowns;

	public SM_ITEM_COOLDOWN(Map<Integer, ItemCooldown> cooldowns) {
		this.cooldowns = cooldowns;
	}

	@Override
	protected void writeImpl(AionConnection con) {
		writeH(cooldowns.size());
		long currentTime = System.currentTimeMillis();
		for (Map.Entry<Integer, ItemCooldown> entry : cooldowns.entrySet()) {
			writeH(entry.getKey());
			int left = (int) ((entry.getValue().getReuseTime() - currentTime) / 1000);
			writeD(left > 0 ? left : 0);
			writeD(entry.getValue().getUseDelay());
		}
	}
}
