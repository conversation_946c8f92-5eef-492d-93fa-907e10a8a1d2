package com.aionemu.gameserver.skillengine.effect;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;

/**
 * <AUTHOR>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AbsoluteStatToPCDebuff")
public class AbsoluteStatToPCDebuffEffect extends AbstractAbsoluteStatEffect {
	// TODO: One skill. Removable by mental healing magic or potion.
	// Relative values - what are they, kinda ADD func ?

	// TODO: set debuff for statfunctions
}
