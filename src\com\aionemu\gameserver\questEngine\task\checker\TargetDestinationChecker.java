package com.aionemu.gameserver.questEngine.task.checker;

import com.aionemu.gameserver.model.gameobjects.Creature;
import com.aionemu.gameserver.utils.PositionUtil;

/**
 * <AUTHOR> Neon
 */
public class TargetDestination<PERSON>hecker extends Des<PERSON><PERSON><PERSON>hecker {

	protected final Creature target;

	public TargetDestinationChecker(Creature follower, Creature target) {
		super(follower);
		this.target = target;
	}

	@Override
	public boolean check() {
		return PositionUtil.isInRange(target, follower, 20);
	}
}
