package com.aionemu.gameserver.configs.main;

import java.util.Set;

import org.quartz.CronExpression;

import com.aionemu.commons.configuration.Property;

public class CustomConfig {

	/**
	 * Enables challenge tasks
	 */
	@Property(key = "gameserver.challenge.tasks.enabled", defaultValue = "false")
	public static boolean CHALLENGE_TASKS_ENABLED;

	/**
	 * Announce when a player successfully enchants an item to +15 or +20
	 */
	@Property(key = "gameserver.enchant.announce.enable", defaultValue = "true")
	public static boolean ENABLE_ENCHANT_ANNOUNCE;

	/**
	 * Enable speaking between factions
	 */
	@Property(key = "gameserver.chat.factions.enable", defaultValue = "false")
	public static boolean SPEAKING_BETWEEN_FACTIONS;

	/**
	 * Minimum level to use whisper
	 */
	@Property(key = "gameserver.chat.whisper.level", defaultValue = "10")
	public static int LEVEL_TO_WHISPER;

	/**
	 * Time in days after which an item in broker will be unregistered (client cannot display more than 255 days)
	 */
	@Property(key = "gameserver.broker.registration_expiration_days", defaultValue = "8")
	public static int BROKER_REGISTRATION_EXPIRATION_DAYS;

	/**
	 * Factions search mode
	 */
	@Property(key = "gameserver.search.factions.mode", defaultValue = "false")
	public static boolean FACTIONS_SEARCH_MODE;

	/**
	 * list gm when search players
	 */
	@Property(key = "gameserver.search.gm.list", defaultValue = "false")
	public static boolean SEARCH_GM_LIST;

	/**
	 * Minimum level to use search
	 */
	@Property(key = "gameserver.search.player.level", defaultValue = "10")
	public static int LEVEL_TO_SEARCH;

	/**
	 * Allow opposite factions to bind in enemy territories
	 */
	@Property(key = "gameserver.cross.faction.binding", defaultValue = "false")
	public static boolean ENABLE_CROSS_FACTION_BINDING;

	/**
	 * Enable second class change without quest
	 */
	@Property(key = "gameserver.simple.secondclass.enable", defaultValue = "false")
	public static boolean ENABLE_SIMPLE_2NDCLASS;


	/**
	 * Disable chain trigger rate (chain skill with 100% success)
	 */
	@Property(key = "gameserver.skill.chain.disable_triggerrate", defaultValue = "false")
	public static boolean SKILL_CHAIN_DISABLE_TRIGGERRATE;

	/**
	 * Base Fly Time
	 */
	@Property(key = "gameserver.base.flytime", defaultValue = "60")
	public static int BASE_FLYTIME;

	@Property(key = "gameserver.friendlist.gm_restrict", defaultValue = "false")
	public static boolean FRIENDLIST_GM_RESTRICT;

	/**
	 * Friendlist size
	 */
	@Property(key = "gameserver.friendlist.size", defaultValue = "90")
	public static int FRIENDLIST_SIZE;

	/**
	 * Basic Quest limit size
	 */
	@Property(key = "gameserver.basic.questsize.limit", defaultValue = "40")
	public static int BASIC_QUEST_SIZE_LIMIT;

	/**
	 * Total number of allowed cube expansions
	 */
	@Property(key = "gameserver.cube.expansion_limit", defaultValue = "11")
	public static int CUBE_EXPANSION_LIMIT;
	
	  @Property(key = "gameserver.enable.add.cube.max", defaultValue = "true")
  public static boolean ENABLE_CUBE_MAX_LIMIT;
  
    @Property(key = "gameserver.basic.cubesize.limit", defaultValue = "9")
  public static int BASIC_CUBE_SIZE_LIMIT;

	/**
	 * Npc Cube Expands limit size
	 */
	@Property(key = "gameserver.npcexpands.limit", defaultValue = "5")
	public static int NPC_CUBE_EXPANDS_SIZE_LIMIT;

	/**
	 * Enable Kinah cap
	 */
	@Property(key = "gameserver.enable.kinah.cap", defaultValue = "false")
	public static boolean ENABLE_KINAH_CAP;

	/**
	 * Kinah cap value
	 */
	@Property(key = "gameserver.kinah.cap.value", defaultValue = "999999999")
	public static long KINAH_CAP_VALUE;

	/**
	 * Enable AP cap
	 */
	@Property(key = "gameserver.enable.ap.cap", defaultValue = "false")
	public static boolean ENABLE_AP_CAP;

	/**
	 * AP cap value
	 */
	@Property(key = "gameserver.ap.cap.value", defaultValue = "1000000")
	public static long AP_CAP_VALUE;

	/**
	 * Enable no AP in mentored group.
	 */
	@Property(key = "gameserver.noap.mentor.group", defaultValue = "false")
	public static boolean MENTOR_GROUP_AP;

	/**
	 * .faction cfg
	 */
	@Property(key = "gameserver.faction.price", defaultValue = "10000")
	public static int FACTION_USE_PRICE;

	@Property(key = "gameserver.faction.cmdchannel", defaultValue = "true")
	public static boolean FACTION_CMD_CHANNEL;

	@Property(key = "gameserver.faction.chatchannels", defaultValue = "false")
	public static boolean FACTION_CHAT_CHANNEL;

	/**
	 * Time in milliseconds in which players are limited for killing one player
	 */
	@Property(key = "gameserver.pvp.dayduration", defaultValue = "86400000")
	public static long PVP_DAY_DURATION;

	/**
	 * Allowed Kills in configuered time for full AP. Move to separate config when more pvp options.
	 */
	@Property(key = "gameserver.pvp.maxkills", defaultValue = "5")
	public static int MAX_DAILY_PVP_KILLS;

	/**
	 * Add a reward to player for pvp kills
	 */
	@Property(key = "gameserver.kill.reward.enable", defaultValue = "false")
	public static boolean ENABLE_KILL_REWARD;

	// ----------------------------
	// Devil's Mark PvP Bounty System config:
	// ----------------------------
	/**
	 * Enable the Devil's Mark PvP bounty system
	 */
	@Property(key = "gameserver.devils.mark.enable", defaultValue = "true")
	public static boolean DEVILS_MARK_ENABLED;

	/**
	 * Minimum level required to be eligible for Devil's Mark
	 */
	@Property(key = "gameserver.devils.mark.min.level", defaultValue = "30")
	public static int DEVILS_MARK_MIN_LEVEL;

	/**
	 * Duration of Devil's Mark in hours
	 */
	@Property(key = "gameserver.devils.mark.duration", defaultValue = "3")
	public static int DEVILS_MARK_DURATION_HOURS;

	/**
	 * AP reward for killing marked player
	 */
	@Property(key = "gameserver.devils.mark.killer.ap", defaultValue = "5000")
	public static int DEVILS_MARK_KILLER_AP_REWARD;

	/**
	 * GP reward for killing marked player
	 */
	@Property(key = "gameserver.devils.mark.killer.gp", defaultValue = "1000")
	public static int DEVILS_MARK_KILLER_GP_REWARD;

	/**
	 * Item rewards for killing marked player (comma-separated item IDs)
	 */
	@Property(key = "gameserver.devils.mark.killer.items", defaultValue = "186000242")
	public static String DEVILS_MARK_KILLER_ITEMS;

	/**
	 * Item counts for killing marked player (comma-separated, matches items)
	 */
	@Property(key = "gameserver.devils.mark.killer.counts", defaultValue = "5")
	public static String DEVILS_MARK_KILLER_COUNTS;

	/**
	 * AP reward for marked player killing normal players
	 */
	@Property(key = "gameserver.devils.mark.marked.killer.ap", defaultValue = "3000")
	public static int DEVILS_MARK_MARKED_KILLER_AP_REWARD;

	/**
	 * GP reward for marked player killing normal players
	 */
	@Property(key = "gameserver.devils.mark.marked.killer.gp", defaultValue = "500")
	public static int DEVILS_MARK_MARKED_KILLER_GP_REWARD;

	/**
	 * Item rewards for marked player killing normal players (comma-separated item IDs)
	 */
	@Property(key = "gameserver.devils.mark.marked.killer.items", defaultValue = "186000242")
	public static String DEVILS_MARK_MARKED_KILLER_ITEMS;

	/**
	 * Item counts for marked player killing normal players (comma-separated, matches items)
	 */
	@Property(key = "gameserver.devils.mark.marked.killer.counts", defaultValue = "3")
	public static String DEVILS_MARK_MARKED_KILLER_COUNTS;

	/**
	 * AP reward for surviving Devil's Mark
	 */
	@Property(key = "gameserver.devils.mark.survival.ap", defaultValue = "2000")
	public static int DEVILS_MARK_SURVIVAL_AP_REWARD;

	/**
	 * Item rewards for surviving Devil's Mark (comma-separated item IDs)
	 */
	@Property(key = "gameserver.devils.mark.survival.items", defaultValue = "186000242")
	public static String DEVILS_MARK_SURVIVAL_ITEMS;

	/**
	 * Item counts for surviving Devil's Mark (comma-separated, matches items)
	 */
	@Property(key = "gameserver.devils.mark.survival.counts", defaultValue = "2")
	public static String DEVILS_MARK_SURVIVAL_COUNTS;

	/**
	 * Visual effect skill IDs for Devil's Mark (comma-separated)
	 */
	@Property(key = "gameserver.devils.mark.visual.effects", defaultValue = "1540")
	public static String DEVILS_MARK_VISUAL_EFFECTS;

	/**
	 * Healing skill IDs for Devil's Mark (comma-separated)
	 */
	@Property(key = "gameserver.devils.mark.healing.skills", defaultValue = "3980")
	public static String DEVILS_MARK_HEALING_SKILLS;

	/**
	 * Healing interval in seconds for Devil's Mark
	 */
	@Property(key = "gameserver.devils.mark.healing.interval", defaultValue = "30")
	public static int DEVILS_MARK_HEALING_INTERVAL;

	/**
	 * Enable healing for Devil's Mark
	 */
	@Property(key = "gameserver.devils.mark.healing.enable", defaultValue = "true")
	public static boolean DEVILS_MARK_HEALING_ENABLE;

	/**
	 * Make marked players vulnerable everywhere (removes safe zone protection)
	 */
	@Property(key = "gameserver.devils.mark.remove.safe.zone.protection", defaultValue = "true")
	public static boolean DEVILS_MARK_REMOVE_SAFE_ZONE_PROTECTION;

	/**
	 * Allow cross-faction targeting (Elyos can attack Asmodian marked players and vice versa)
	 */
	@Property(key = "gameserver.devils.mark.cross.faction.targeting", defaultValue = "true")
	public static boolean DEVILS_MARK_CROSS_FACTION_TARGETING;

	/**
	 * Enable one kisk restriction
	 */
	@Property(key = "gameserver.kisk.restriction.enable", defaultValue = "true")
	public static boolean ENABLE_KISK_RESTRICTION;

	@Property(key = "gameserver.rift.enable", defaultValue = "true")
	public static boolean RIFT_ENABLED;
	@Property(key = "gameserver.rift.duration", defaultValue = "1")
	public static int RIFT_DURATION;

	@Property(key = "gameserver.vortex.enable", defaultValue = "true")
	public static boolean VORTEX_ENABLED;
	@Property(key = "gameserver.vortex.brusthonin.schedule", defaultValue = "0 0 16 ? * SAT")
	public static CronExpression VORTEX_BRUSTHONIN_SCHEDULE;
	@Property(key = "gameserver.vortex.theobomos.schedule", defaultValue = "0 0 16 ? * SUN")
	public static CronExpression VORTEX_THEOBOMOS_SCHEDULE;
	@Property(key = "gameserver.vortex.duration", defaultValue = "1")
	public static int VORTEX_DURATION;

	@Property(key = "gameserver.cp.enable", defaultValue = "true")
	public static boolean CONQUEROR_AND_PROTECTOR_SYSTEM_ENABLED;
	@Property(key = "gameserver.cp.worlds", defaultValue = "210020000,210040000,210050000,210070000,220020000,220040000,220070000,220080000")
	public static Set<Integer> CONQUEROR_AND_PROTECTOR_WORLDS;
	@Property(key = "gameserver.cp.level.diff", defaultValue = "5")
	public static int CONQUEROR_AND_PROTECTOR_LEVEL_DIFF;
	@Property(key = "gameserver.cp.kills.decrease_interval_minutes", defaultValue = "10")
	public static int CONQUEROR_AND_PROTECTOR_KILLS_DECREASE_INTERVAL;
	@Property(key = "gameserver.cp.kills.decrease_count", defaultValue = "1")
	public static int CONQUEROR_AND_PROTECTOR_KILLS_DECREASE_COUNT;
	@Property(key = "gameserver.cp.kills.rank1", defaultValue = "1")
	public static int CONQUEROR_AND_PROTECTOR_KILLS_RANK1;
	@Property(key = "gameserver.cp.kills.rank2", defaultValue = "10")
	public static int CONQUEROR_AND_PROTECTOR_KILLS_RANK2;
	@Property(key = "gameserver.cp.kills.rank3", defaultValue = "20")
	public static int CONQUEROR_AND_PROTECTOR_KILLS_RANK3;

	/**
	 * Limits Config
	 */
	@Property(key = "gameserver.limits.enable", defaultValue = "true")
	public static boolean LIMITS_ENABLED;

	@Property(key = "gameserver.limits.enable_dynamic_cap", defaultValue = "false")
	public static boolean LIMITS_ENABLE_DYNAMIC_CAP;

	@Property(key = "gameserver.limits.update", defaultValue = "0 0 0 ? * *")
	public static CronExpression LIMITS_UPDATE;

	@Property(key = "gameserver.abyssxform.afterlogout", defaultValue = "false")
	public static boolean ABYSSXFORM_LOGOUT;

	@Property(key = "gameserver.ride.restriction.enable", defaultValue = "true")
	public static boolean ENABLE_RIDE_RESTRICTION;

	/**
	 * Enables sell apitems
	 */
	@Property(key = "gameserver.selling.apitems.enabled", defaultValue = "true")
	public static boolean SELLING_APITEMS_ENABLED;

	@Property(key = "character.deletion.time.minutes", defaultValue = "5")
	public static int CHARACTER_DELETION_TIME_MINUTES;

	/**
	 * Custom Reward Packages
	 */
	@Property(key = "gameserver.custom.starter_kit.enable", defaultValue = "false")
	public static boolean ENABLE_STARTER_KIT;

	@Property(key = "gameserver.pvpmap.enable", defaultValue = "false")
	public static boolean PVP_MAP_ENABLED;

	@Property(key = "gameserver.pvpmap.apmultiplier", defaultValue = "2")
	public static float PVP_MAP_AP_MULTIPLIER;

	@Property(key = "gameserver.pvpmap.pve.apmultiplier", defaultValue = "1")
	public static float PVP_MAP_PVE_AP_MULTIPLIER;

	@Property(key = "gameserver.pvpmap.random_boss.rate", defaultValue = "40")
	public static int PVP_MAP_RANDOM_BOSS_BASE_RATE;

	@Property(key = "gameserver.pvpmap.random_boss.time", defaultValue = "0 30 14,18,21 ? * *")
	public static CronExpression PVP_MAP_RANDOM_BOSS_SCHEDULE;

	@Property(key = "gameserver.rates.godstone.activation.rate", defaultValue = "1.0")
	public static float GODSTONE_ACTIVATION_RATE;

	@Property(key = "gameserver.rates.godstone.evaluation.cooldown_millis", defaultValue = "750")
	public static int GODSTONE_EVALUATION_COOLDOWN_MILLIS;

	// ----------------------------
	// OneVsOne PvP config:
	// ----------------------------
	@Property(key = "gameserver.onevsone.enable", defaultValue = "true")
	public static boolean ONEVSONE_ENABLED;

	@Property(key = "gameserver.onevsone.min.level", defaultValue = "30")
	public static int ONEVSONE_MIN_LEVEL;

	@Property(key = "gameserver.onevsone.max.level.diff", defaultValue = "10")
	public static int ONEVSONE_MAX_LEVEL_DIFF;

	@Property(key = "gameserver.onevsone.queue.timeout", defaultValue = "10")
	public static int ONEVSONE_QUEUE_TIMEOUT_MINUTES;

	@Property(key = "gameserver.onevsone.match.duration", defaultValue = "15")
	public static int ONEVSONE_MATCH_DURATION_MINUTES;

	@Property(key = "gameserver.onevsone.maps", defaultValue = "300030000,300040000,300250000")
	public static String ONEVSONE_MAPS;

	@Property(key = "gameserver.onevsone.portal.npc", defaultValue = "207011")
	public static int ONEVSONE_PORTAL_NPC;

	@Property(key = "gameserver.onevsone.portal.locations", defaultValue = "110010000:1321.9839,1513.3536,567.9099,0;120010000:1664.8394,1399.1025,194.66542,0")
	public static String ONEVSONE_PORTAL_LOCATIONS;

	@Property(key = "gameserver.onevsone.portal.name", defaultValue = "OneVsOne Portal")
	public static String ONEVSONE_PORTAL_NAME;

	@Property(key = "gameserver.onevsone.rewards.winner", defaultValue = "186000409:300:100000")
	public static String ONEVSONE_REWARDS_WINNER;

	@Property(key = "gameserver.onevsone.rewards.loser", defaultValue = "186000409:150:25000")
	public static String ONEVSONE_REWARDS_LOSER;

	@Property(key = "gameserver.onevsone.announcements", defaultValue = "true")
	public static boolean ONEVSONE_ANNOUNCEMENTS;

	@Property(key = "gameserver.onevsone.cross.faction", defaultValue = "true")
	public static boolean ONEVSONE_CROSS_FACTION;

	@Property(key = "gameserver.onevsone.auto.start", defaultValue = "true")
	public static boolean ONEVSONE_AUTO_START;

	@Property(key = "gameserver.onevsone.schedule", defaultValue = "0 0 12,18 * * ?")
	public static String ONEVSONE_SCHEDULE;

	@Property(key = "gameserver.onevsone.spawn.300030000", defaultValue = "366.64902,490.62766,347.25732,86;357.15436,440.91724,361.19855,25")
	public static String ONEVSONE_SPAWN_300030000;

	@Property(key = "gameserver.onevsone.spawn.300040000", defaultValue = "374.36356,1297.2429,158.21782,55;277.59238,1313.0216,161.07675,119")
	public static String ONEVSONE_SPAWN_300040000;

	@Property(key = "gameserver.onevsone.spawn.300250000", defaultValue = "1086.4783,625.78796,266.69022,94;1099.499,574.3,266.56262,33")
	public static String ONEVSONE_SPAWN_300250000;

	// ----------------------------
	// MixFight Event config:
	// ----------------------------
	@Property(key = "gameserver.mixfight.enable", defaultValue = "true")
	public static boolean MIXFIGHT_ENABLED;

	@Property(key = "gameserver.mixfight.min.level", defaultValue = "50")
	public static int MIXFIGHT_MIN_LEVEL;

	@Property(key = "gameserver.mixfight.duration", defaultValue = "60")
	public static int MIXFIGHT_DURATION_MINUTES;

	@Property(key = "gameserver.mixfight.portal.duration", defaultValue = "15")
	public static int MIXFIGHT_PORTAL_DURATION_MINUTES;

	@Property(key = "gameserver.mixfight.schedule", defaultValue = "0 0 14,20 * * ?")
	public static String MIXFIGHT_SCHEDULE;

	@Property(key = "gameserver.mixfight.maps", defaultValue = "300030000,300040000,300250000")
	public static String MIXFIGHT_MAPS;

	@Property(key = "gameserver.mixfight.portal.sanctum.npc", defaultValue = "831073")
	public static int MIXFIGHT_PORTAL_SANCTUM_NPC;

	@Property(key = "gameserver.mixfight.portal.pandaemonium.npc", defaultValue = "831073")
	public static int MIXFIGHT_PORTAL_PANDAEMONIUM_NPC;

	@Property(key = "gameserver.mixfight.portal.sanctum.location", defaultValue = "1321.9839,1513.3536,567.9099,0")
	public static String MIXFIGHT_PORTAL_SANCTUM_LOCATION;

	@Property(key = "gameserver.mixfight.portal.pandaemonium.location", defaultValue = "1664.8394,1399.1025,194.66542,0")
	public static String MIXFIGHT_PORTAL_PANDAEMONIUM_LOCATION;

	@Property(key = "gameserver.mixfight.portal.name", defaultValue = "MIXFIGHT PORTAL")
	public static String MIXFIGHT_PORTAL_NAME;

	@Property(key = "gameserver.mixfight.points.multiplier", defaultValue = "2.0")
	public static float MIXFIGHT_POINTS_MULTIPLIER;

	@Property(key = "gameserver.mixfight.rewards.tier1", defaultValue = "10000:186000409:500:500000:500")
	public static String MIXFIGHT_REWARDS_TIER1;

	@Property(key = "gameserver.mixfight.rewards.tier2", defaultValue = "5000:186000409:400:250000:400")
	public static String MIXFIGHT_REWARDS_TIER2;

	@Property(key = "gameserver.mixfight.rewards.tier3", defaultValue = "2000:186000409:300:100000:300")
	public static String MIXFIGHT_REWARDS_TIER3;

	@Property(key = "gameserver.mixfight.rewards.tier4", defaultValue = "1000:186000409:250:50000:200")
	public static String MIXFIGHT_REWARDS_TIER4;

	@Property(key = "gameserver.mixfight.rewards.participation", defaultValue = "186000409:100:25000:50")
	public static String MIXFIGHT_REWARDS_PARTICIPATION;

	@Property(key = "gameserver.mixfight.max.players", defaultValue = "50")
	public static int MIXFIGHT_MAX_PLAYERS;

	@Property(key = "gameserver.mixfight.announcements", defaultValue = "true")
	public static boolean MIXFIGHT_ANNOUNCEMENTS;

	@Property(key = "gameserver.mixfight.announcement.intervals", defaultValue = "30,15,5,1")
	public static String MIXFIGHT_ANNOUNCEMENT_INTERVALS;

	// MixFight spawn coordinates
	@Property(key = "gameserver.mixfight.spawn.300030000", defaultValue = "516.2301,665.2475,330.98575,80")
	public static String MIXFIGHT_SPAWN_300030000;

	@Property(key = "gameserver.mixfight.spawn.300040000", defaultValue = "1230.3514,409.39297,140.125,67")
	public static String MIXFIGHT_SPAWN_300040000;

	@Property(key = "gameserver.mixfight.spawn.300250000", defaultValue = "844.81775,578.80804,180.85222,110")
	public static String MIXFIGHT_SPAWN_300250000;

	// ----------------------------
	// FFA (Free-For-All) Event config:
	// ----------------------------
	@Property(key = "gameserver.ffa.enable", defaultValue = "true")
	public static boolean FFA_ENABLED;

	@Property(key = "gameserver.ffa.min.level", defaultValue = "50")
	public static int FFA_MIN_LEVEL;

	@Property(key = "gameserver.ffa.duration", defaultValue = "30")
	public static int FFA_DURATION_MINUTES;

	@Property(key = "gameserver.ffa.max.players", defaultValue = "100")
	public static int FFA_MAX_PLAYERS;

	@Property(key = "gameserver.ffa.schedule", defaultValue = "0 0 16,22 * * ?")
	public static String FFA_SCHEDULE;

	@Property(key = "gameserver.ffa.auto.start", defaultValue = "false")
	public static boolean FFA_AUTO_START;

	@Property(key = "gameserver.ffa.announcements", defaultValue = "true")
	public static boolean FFA_ANNOUNCEMENTS;

	@Property(key = "gameserver.ffa.map.id", defaultValue = "300040000")
	public static int FFA_MAP_ID;

	@Property(key = "gameserver.ffa.maps", defaultValue = "300030000,300040000,300250000")
	public static String FFA_MAPS;

	@Property(key = "gameserver.ffa.spawn.coords", defaultValue = "242.52405,424.71637,103.80612,0")
	public static String FFA_SPAWN_COORDS;

	@Property(key = "gameserver.ffa.spawn.300030000", defaultValue = "516.2301,665.2475,330.98575,80;505.0609,556.5992,331.875,24;356.20883,649.88837,374.92065,94;361.09503,594.8203,343.4533,110;504.8307,552.8712,331.73163,28;396.7961,434.89804,367.375,49;319.3241,482.66284,362.1666,113;378.74472,407.0372,376.87595,33;361.61758,426.42648,378.05753,73;396.9945,284.7902,378.83838,30;285.6835,349.3712,380.09332,6;337.03836,356.89923,392.41467,30;331.6864,271.93954,384.55338,25;383.3997,504.6931,360.22946,20;430.06927,503.45038,359.9095,70")
	public static String FFA_SPAWN_300030000;

	@Property(key = "gameserver.ffa.spawn.300040000", defaultValue = "1230.3514,409.39297,140.125,67;1052.1534,311.02072,132.80185,41;972.8223,530.3805,101.175385,78;832.4362,577.91846,118.75,99;641.57635,354.30344,103.125,19;557.3073,490.47943,107.55174,2;517.4669,671.3205,115.58252,8;689.1145,861.0198,125.9925,62;581.9522,971.206,128.01974,79;433.6868,1056.9828,119.75,9;561.6822,1151.2819,139.39552,69;334.62918,1175.837,151.73123,31;234.94008,1302.3478,150.77277,114;375.23392,1316.1445,156.88617,80;361.7095,1247.0088,155.23184,97")
	public static String FFA_SPAWN_300040000;

	@Property(key = "gameserver.ffa.spawn.300250000", defaultValue = "844.81775,578.80804,180.85222,110;952.45197,424.6489,218.1969,7;1006.8982,514.4989,248.64395,27;967.2982,657.34344,256.875,38;1225.7998,483.6569,265.76254,82;1297.7977,609.86475,296.66135,20;1238.2762,577.2078,294.89227,104;1272.1031,774.35925,261.0,43;1224.0793,963.01166,326.98068,93;1131.2866,886.30255,316.6489,80;1116.6604,817.08105,316.79816,0;1076.9366,970.67316,322.39038,92;1251.3419,866.95953,320.96628,40;1285.4011,891.6742,318.875,39;1026.0908,794.0275,257.75,65")
	public static String FFA_SPAWN_300250000;

	@Property(key = "gameserver.ffa.rewards.first", defaultValue = "186000409:500:500000")
	public static String FFA_REWARDS_FIRST;

	@Property(key = "gameserver.ffa.rewards.second", defaultValue = "186000409:300:300000")
	public static String FFA_REWARDS_SECOND;

	@Property(key = "gameserver.ffa.rewards.third", defaultValue = "186000409:200:200000")
	public static String FFA_REWARDS_THIRD;

	@Property(key = "gameserver.ffa.rewards.participation", defaultValue = "186000409:100:50000")
	public static String FFA_REWARDS_PARTICIPATION;

	@Property(key = "gameserver.ffa.rewards", defaultValue = "1st: 186000409:500:500000, 2nd: 186000409:300:300000, 3rd: 186000409:200:200000, Participation: 186000409:100:50000")
	public static String FFA_REWARDS;

	@Property(key = "gameserver.ffa.teleport.elyos", defaultValue = "110010000,1313.25,1512.011,568.107,0")
	public static String FFA_TELEPORT_ELYOS;

	@Property(key = "gameserver.ffa.teleport.asmodians", defaultValue = "120010000,1685.7,1400.5,195.48618,60")
	public static String FFA_TELEPORT_ASMODIANS;
}
